import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/feature/host/presentation/cubit/host_dashboard_cubit.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/shimmer_loading.dart';
import 'package:gather_point/feature/host/presentation/views/reservation_details_page.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class HostReservationsPage extends StatefulWidget {
  const HostReservationsPage({super.key});

  @override
  State<HostReservationsPage> createState() => _HostReservationsPageState();
}

class _HostReservationsPageState extends State<HostReservationsPage> {
  String _selectedFilter = 'all';
  final ReservationsApiService _reservationsApiService = getIt<ReservationsApiService>();

  @override
  void initState() {
    super.initState();
    _loadReservations();
  }

  void _loadReservations() {
    context.read<HostDashboardCubit>().loadHostReservations(
      status: _selectedFilter == 'all' ? null : _selectedFilter,
    );
  }

  Future<void> _onRefresh() async {
    _loadReservations();
    // Add a small delay for better UX
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: 'إدارة الحجوزات',
      hasBottomNavigation: false,
      body: Column(
        children: [
          // Quick Stats
          BlocBuilder<HostDashboardCubit, HostDashboardState>(
            builder: (context, state) {
              if (state is HostReservationsLoaded) {
                return _buildQuickStats(state.reservations);
              }
              return const SizedBox.shrink();
            },
          ),

          // Filter Tabs
          _buildFilterTabs(s),

          // Reservations List
          Expanded(
            child: BlocBuilder<HostDashboardCubit, HostDashboardState>(
              builder: (context, state) {
                if (state is HostReservationsLoading) {
                  return _buildShimmerLoading();
                }
                
                if (state is HostReservationsError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'خطأ في تحميل الحجوزات',
                          style: AppTextStyles.font16SemiBold.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: AppTextStyles.font14Regular.copyWith(
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _loadReservations,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.yellow,
                            foregroundColor: AppColors.black,
                          ),
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }
                
                if (state is HostReservationsLoaded) {
                  final reservations = state.reservations;
                  
                  if (reservations.isEmpty) {
                    return _buildEmptyState(s);
                  }
                  
                  return RefreshIndicator(
                    onRefresh: _onRefresh,
                    color: AppColors.yellow,
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: reservations.length,
                      itemBuilder: (context, index) {
                        final reservation = reservations[index];
                        return AnimatedContainer(
                          duration: Duration(milliseconds: 300 + (index * 50)),
                          curve: Curves.easeOutBack,
                          child: _buildReservationCard(reservation, s),
                        );
                      },
                    ),
                  );
                }
                
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildFilterTab('all', 'الكل', _selectedFilter == 'all'),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterTab('pending', 'في الانتظار', _selectedFilter == 'pending'),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterTab('confirmed', 'مؤكدة', _selectedFilter == 'confirmed'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String value, String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = value;
        });
        _loadReservations();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.yellow : Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: AppTextStyles.font14SemiBold.copyWith(
            color: isSelected ? AppColors.black : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(S s) {
    return Center(
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 800),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: value,
            child: Opacity(
              opacity: value,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.yellow.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.calendar_today_outlined,
                      size: 48,
                      color: AppColors.yellow,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'لا توجد حجوزات',
                    style: AppTextStyles.font18Bold.copyWith(
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      'ستظهر هنا الحجوزات الخاصة بعقاراتك',
                      style: AppTextStyles.font14Regular.copyWith(
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildReservationCard(ReservationModel reservation, S s) {
    final isConfirmed = reservation.confirmed;
    final statusColor = isConfirmed ? Colors.green : Colors.orange;
    final statusText = isConfirmed ? 'مؤكدة' : 'في الانتظار';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showReservationDetails(reservation),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      'حجز #${reservation.id}',
                      style: AppTextStyles.font16Bold,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      statusText,
                      style: AppTextStyles.font12SemiBold.copyWith(
                        color: statusColor,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Property info with image
              if (reservation.item != null) ...[
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[200],
                        child: reservation.item!.image != null
                            ? Image.network(
                                reservation.item!.image!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(Icons.home, color: Colors.grey[400]);
                                },
                              )
                            : Icon(Icons.home, color: Colors.grey[400]),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            reservation.item!.title,
                            style: AppTextStyles.font14SemiBold,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'السعر: ${reservation.item!.price.toStringAsFixed(0)} ر.س/ليلة',
                            style: AppTextStyles.font12Regular.copyWith(
                              color: AppColors.yellow,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
              ],

              // Guest and reservation details
              _buildDetailRow('العميل:', reservation.guestName ?? 'غير محدد'),
              if (reservation.guestPhone != null)
                _buildDetailRow('الهاتف:', reservation.guestPhone!),
              _buildDetailRow(
                'من:',
                DateFormat('yyyy/MM/dd HH:mm').format(
                  DateTime.parse(reservation.reservationFrom),
                ),
              ),
              _buildDetailRow(
                'إلى:',
                DateFormat('yyyy/MM/dd HH:mm').format(
                  DateTime.parse(reservation.reservationTo),
                ),
              ),
              _buildDetailRow('المدة:', '${reservation.durationInDays} أيام'),
              _buildDetailRow(
                'المبلغ الإجمالي:',
                '${reservation.totalAmount.toStringAsFixed(0)} ر.س',
                valueStyle: AppTextStyles.font14SemiBold.copyWith(
                  color: AppColors.yellow,
                ),
              ),

              const SizedBox(height: 16),

              // Action buttons
              _buildActionButtons(reservation, isConfirmed),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: valueStyle ?? AppTextStyles.font14SemiBold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ReservationModel reservation, bool isConfirmed) {
    if (isConfirmed) {
      return Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _showReservationDetails(reservation),
              icon: const Icon(Icons.visibility),
              label: const Text('عرض التفاصيل'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.yellow,
                side: const BorderSide(color: AppColors.yellow),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _contactGuest(reservation),
              icon: const Icon(Icons.phone),
              label: const Text('اتصال'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.green,
                side: const BorderSide(color: Colors.green),
              ),
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showConfirmationDialog(reservation),
            icon: const Icon(Icons.check_circle),
            label: const Text('قبول'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showDeclineDialog(reservation),
            icon: const Icon(Icons.cancel),
            label: const Text('رفض'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _showReservationDetails(ReservationModel reservation) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReservationDetailsPage(reservation: reservation),
      ),
    );

    // If the reservation was updated, refresh the list
    if (result == true) {
      _loadReservations();
    }
  }

  void _contactGuest(ReservationModel reservation) async {
    if (reservation.guestPhone != null) {
      final uri = Uri(scheme: 'tel', path: reservation.guestPhone!);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لا يمكن إجراء المكالمة')),
          );
        }
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('رقم الهاتف غير متوفر')),
      );
    }
  }

  void _showConfirmationDialog(ReservationModel reservation) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.check_circle, color: Colors.green),
            ),
            const SizedBox(width: 12),
            const Expanded(child: Text('تأكيد قبول الحجز')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل أنت متأكد من قبول الحجز التالي؟'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حجز #${reservation.id}',
                    style: AppTextStyles.font14SemiBold,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'العميل: ${reservation.guestName ?? 'غير محدد'}',
                    style: AppTextStyles.font12Regular,
                  ),
                  Text(
                    'العقار: ${reservation.item?.title ?? 'غير محدد'}',
                    style: AppTextStyles.font12Regular,
                  ),
                  Text(
                    'المدة: ${reservation.durationInDays} أيام',
                    style: AppTextStyles.font12Regular,
                  ),
                  Text(
                    'المبلغ: ${reservation.totalAmount.toStringAsFixed(0)} ر.س',
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: AppColors.yellow,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم إرسال تأكيد الحجز للعميل فوراً',
                      style: AppTextStyles.font12Regular.copyWith(
                        color: Colors.green[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _confirmReservationWithLoading(reservation);
            },
            icon: const Icon(Icons.check_circle),
            label: const Text('تأكيد القبول'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeclineDialog(ReservationModel reservation) {
    String declineReason = '';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.cancel, color: Colors.red),
            ),
            const SizedBox(width: 12),
            const Expanded(child: Text('رفض الحجز')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل أنت متأكد من رفض الحجز التالي؟'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حجز #${reservation.id}',
                    style: AppTextStyles.font14SemiBold,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'العميل: ${reservation.guestName ?? 'غير محدد'}',
                    style: AppTextStyles.font12Regular,
                  ),
                  Text(
                    'العقار: ${reservation.item?.title ?? 'غير محدد'}',
                    style: AppTextStyles.font12Regular,
                  ),
                  Text(
                    'المدة: ${reservation.durationInDays} أيام',
                    style: AppTextStyles.font12Regular,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سبب الرفض (اختياري):',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: InputDecoration(
                hintText: 'مثال: العقار غير متاح في هذه التواريخ',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
              maxLines: 3,
              onChanged: (value) {
                declineReason = value;
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم إشعار العميل برفض الحجز',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _rejectReservationWithLoading(reservation, declineReason);
            },
            icon: const Icon(Icons.cancel),
            label: const Text('تأكيد الرفض'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmReservationWithLoading(ReservationModel reservation) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: AppColors.yellow),
            const SizedBox(height: 16),
            const Text('جاري قبول الحجز...'),
            const SizedBox(height: 8),
            Text(
              'حجز #${reservation.id}',
              style: AppTextStyles.font12Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );

    try {
      await _reservationsApiService.confirmReservation(reservation.id);

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show success dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check_circle, color: Colors.green),
                ),
                const SizedBox(width: 12),
                const Expanded(child: Text('تم قبول الحجز')),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('تم قبول الحجز بنجاح وإرسال التأكيد للعميل'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.notifications_active, color: Colors.green),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'سيتم إشعار العميل عبر الرسائل النصية',
                          style: AppTextStyles.font12Regular,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _loadReservations(); // Refresh the list
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.error, color: Colors.red),
                ),
                const SizedBox(width: 12),
                const Expanded(child: Text('خطأ في قبول الحجز')),
              ],
            ),
            content: Text('حدث خطأ أثناء قبول الحجز: ${e.toString()}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _confirmReservationWithLoading(reservation); // Retry
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: AppColors.black,
                ),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }
    }
  }



  Future<void> _rejectReservationWithLoading(ReservationModel reservation, String reason) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: AppColors.yellow),
            const SizedBox(height: 16),
            const Text('جاري رفض الحجز...'),
            const SizedBox(height: 8),
            Text(
              'حجز #${reservation.id}',
              style: AppTextStyles.font12Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );

    try {
      await _reservationsApiService.rejectReservation(reservation.id, reason: reason);

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show success dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.cancel, color: Colors.orange),
                ),
                const SizedBox(width: 12),
                const Expanded(child: Text('تم رفض الحجز')),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('تم رفض الحجز وإشعار العميل'),
                if (reason.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  const Text(
                    'سبب الرفض:',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(reason),
                  ),
                ],
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.notifications_active, color: Colors.orange),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'تم إشعار العميل برفض الحجز',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _loadReservations(); // Refresh the list
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.error, color: Colors.red),
                ),
                const SizedBox(width: 12),
                const Expanded(child: Text('خطأ في رفض الحجز')),
              ],
            ),
            content: Text('حدث خطأ أثناء رفض الحجز: ${e.toString()}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _rejectReservationWithLoading(reservation, reason); // Retry
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: AppColors.black,
                ),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }
    }
  }



  Widget _buildShimmerLoading() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return ShimmerLoading(
          isLoading: true,
          child: Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const ShimmerBox(width: 120, height: 20),
                      ShimmerBox(width: 80, height: 24, borderRadius: BorderRadius.circular(12)),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const ShimmerBox(width: double.infinity, height: 16),
                  const SizedBox(height: 8),
                  const ShimmerBox(width: 200, height: 16),
                  const SizedBox(height: 8),
                  const ShimmerBox(width: 150, height: 16),
                  const SizedBox(height: 8),
                  const ShimmerBox(width: 180, height: 16),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(child: ShimmerBox(width: double.infinity, height: 40, borderRadius: BorderRadius.circular(8))),
                      const SizedBox(width: 12),
                      Expanded(child: ShimmerBox(width: double.infinity, height: 40, borderRadius: BorderRadius.circular(8))),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(List<ReservationModel> reservations) {
    final confirmedCount = reservations.where((r) => r.confirmed).length;
    final pendingCount = reservations.where((r) => !r.confirmed).length;
    final totalRevenue = reservations
        .where((r) => r.confirmed)
        .fold(0.0, (sum, r) => sum + r.totalAmount);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.yellow.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.yellow.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.check_circle,
              label: 'مؤكدة',
              value: confirmedCount.toString(),
              color: Colors.green,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.pending,
              label: 'في الانتظار',
              value: pendingCount.toString(),
              color: Colors.orange,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.attach_money,
              label: 'الإيرادات',
              value: '${totalRevenue.toStringAsFixed(0)} ر.س',
              color: AppColors.yellow,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.font16Bold.copyWith(color: color),
        ),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }


}
